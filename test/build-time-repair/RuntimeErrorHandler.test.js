const RuntimeErrorHandler = require('../../src/build-time-repair/RuntimeErrorHandler');
const express = require('express');
const request = require('supertest');
const path = require('path');

describe('RuntimeErrorHandler', () => {
  let runtimeErrorHandler;
  let app;
  const testProjectPath = path.join(__dirname, '../fixtures/test-project');

  beforeEach(() => {
    runtimeErrorHandler = new RuntimeErrorHandler(testProjectPath, {
      port: 3001,
      maxErrors: 10,
      autoFix: false, // 测试时禁用自动修复
      verbose: false
    });

    // 创建测试Express应用
    app = express();
    app.use(express.json());
    app.use(runtimeErrorHandler.getRouter());
  });

  afterEach(() => {
    runtimeErrorHandler.clearErrorHistory();
  });

  describe('错误接收和处理', () => {
    test('应该能够接收运行时错误', async () => {
      const errorData = {
        message: 'Cannot read property of undefined',
        fileName: 'src/components/Test.vue',
        lineNumber: 25,
        columnNumber: 10,
        stack: 'Error: Cannot read property of undefined\n    at Test.vue:25:10',
        componentTrace: [
          { name: 'TestComponent', file: 'src/components/Test.vue', line: 25 }
        ],
        timestamp: new Date().toISOString(),
        type: 'vue-error'
      };

      const response = await request(app)
        .post('/__runtime_errors__')
        .send(errorData)
        .expect(200);

      expect(response.body.success).toBe(true);
      
      const stats = runtimeErrorHandler.getErrorStats();
      expect(stats.totalErrors).toBe(1);
      expect(stats.fixedErrors).toBe(0);
      expect(stats.pendingErrors).toBe(1);
    });

    test('应该能够去重相同的错误', async () => {
      const errorData = {
        message: 'Same error',
        fileName: 'src/components/Test.vue',
        lineNumber: 25,
        columnNumber: 10
      };

      // 发送相同错误两次
      await request(app)
        .post('/__runtime_errors__')
        .send(errorData)
        .expect(200);

      await request(app)
        .post('/__runtime_errors__')
        .send(errorData)
        .expect(200);

      const stats = runtimeErrorHandler.getErrorStats();
      expect(stats.totalErrors).toBe(1); // 应该只有一个唯一错误
    });

    test('应该能够获取错误历史', async () => {
      const errorData = {
        message: 'Test error',
        fileName: 'src/components/Test.vue',
        lineNumber: 25
      };

      await request(app)
        .post('/__runtime_errors__')
        .send(errorData);

      const response = await request(app)
        .get('/__runtime_errors__')
        .expect(200);

      expect(response.body.errors).toHaveLength(1);
      expect(response.body.queueLength).toBe(1);
      expect(response.body.errors[0].message).toBe('Test error');
    });

    test('应该能够清除错误历史', async () => {
      const errorData = {
        message: 'Test error',
        fileName: 'src/components/Test.vue',
        lineNumber: 25
      };

      await request(app)
        .post('/__runtime_errors__')
        .send(errorData);

      // 清除错误历史
      await request(app)
        .delete('/__runtime_errors__')
        .expect(200);

      const stats = runtimeErrorHandler.getErrorStats();
      expect(stats.totalErrors).toBe(0);
    });
  });

  describe('错误ID生成', () => {
    test('应该为相同错误生成相同ID', () => {
      const errorData1 = {
        fileName: 'test.vue',
        lineNumber: 10,
        message: 'test error'
      };

      const errorData2 = {
        fileName: 'test.vue',
        lineNumber: 10,
        message: 'test error'
      };

      const id1 = runtimeErrorHandler.generateErrorId(errorData1);
      const id2 = runtimeErrorHandler.generateErrorId(errorData2);

      expect(id1).toBe(id2);
    });

    test('应该为不同错误生成不同ID', () => {
      const errorData1 = {
        fileName: 'test1.vue',
        lineNumber: 10,
        message: 'test error'
      };

      const errorData2 = {
        fileName: 'test2.vue',
        lineNumber: 10,
        message: 'test error'
      };

      const id1 = runtimeErrorHandler.generateErrorId(errorData1);
      const id2 = runtimeErrorHandler.generateErrorId(errorData2);

      expect(id1).not.toBe(id2);
    });
  });

  describe('错误上下文构建', () => {
    test('应该正确构建错误上下文', () => {
      const errorRecord = {
        fileName: 'src/components/Test.vue',
        lineNumber: 25,
        columnNumber: 10,
        message: 'Cannot read property of undefined',
        stack: 'Error stack trace',
        componentTrace: [
          { name: 'TestComponent', file: 'src/components/Test.vue', line: 25 }
        ],
        firstOccurred: new Date(),
        count: 1
      };

      const context = runtimeErrorHandler.buildErrorContext(errorRecord);

      expect(context.type).toBe('runtime');
      expect(context.fileName).toBe('src/components/Test.vue');
      expect(context.lineNumber).toBe(25);
      expect(context.message).toBe('Cannot read property of undefined');
      expect(context.buildOutput).toContain('Runtime Error in src/components/Test.vue:25');
      expect(context.buildOutput).toContain('Vue Component Trace:');
    });
  });

  describe('错误格式化', () => {
    test('应该正确格式化为构建错误输出', () => {
      const errorRecord = {
        fileName: 'src/components/Test.vue',
        lineNumber: 25,
        message: 'Test error',
        stack: 'Error stack',
        componentTrace: [
          { name: 'TestComponent', file: 'src/components/Test.vue', line: 25 },
          { name: 'ParentComponent', file: 'src/components/Parent.vue', line: 15 }
        ]
      };

      const output = runtimeErrorHandler.formatAsBuilderError(errorRecord);

      expect(output).toContain('Runtime Error in src/components/Test.vue:25');
      expect(output).toContain('Error: Test error');
      expect(output).toContain('Stack Trace:');
      expect(output).toContain('Vue Component Trace:');
      expect(output).toContain('1. TestComponent (src/components/Test.vue:25)');
      expect(output).toContain('2. ParentComponent (src/components/Parent.vue:15)');
    });
  });

  describe('错误统计', () => {
    test('应该正确计算错误统计信息', () => {
      // 初始状态
      let stats = runtimeErrorHandler.getErrorStats();
      expect(stats.totalErrors).toBe(0);
      expect(stats.fixedErrors).toBe(0);
      expect(stats.fixRate).toBe(0);

      // 添加一些错误
      runtimeErrorHandler.errorHistory.set('error1', { fixed: false });
      runtimeErrorHandler.errorHistory.set('error2', { fixed: true });
      runtimeErrorHandler.errorHistory.set('error3', { fixed: true });

      stats = runtimeErrorHandler.getErrorStats();
      expect(stats.totalErrors).toBe(3);
      expect(stats.fixedErrors).toBe(2);
      expect(stats.fixRate).toBe('66.7');
    });
  });

  describe('注入代码生成', () => {
    test('应该生成有效的注入代码', () => {
      const injectionCode = runtimeErrorHandler.generateInjectionCode();

      expect(injectionCode).toContain('RUNTIME_ERROR_ENDPOINT');
      expect(injectionCode).toContain('sendErrorToServer');
      expect(injectionCode).toContain('window.Vue.config.errorHandler');
      expect(injectionCode).toContain('window.Vue.config.warnHandler');
      expect(injectionCode).toContain('window.addEventListener(\'error\'');
      expect(injectionCode).toContain('window.addEventListener(\'unhandledrejection\'');
      expect(injectionCode).toContain('getComponentTrace');
    });

    test('注入代码应该包含正确的端点', () => {
      const customHandler = new RuntimeErrorHandler(testProjectPath, {
        errorEndpoint: '/custom-errors'
      });

      const injectionCode = customHandler.generateInjectionCode();
      expect(injectionCode).toContain('/custom-errors');
    });
  });
});
