/**
 * 运行时错误修复集成示例
 * 
 * 展示如何将运行时错误修复集成到现有的迁移流程中
 */

const path = require('path');
const chalk = require('chalk');
const MigrationOrchestrator = require('../migration/MigrationOrchestrator');
const RuntimeErrorRepairPhase = require('./RuntimeErrorRepairPhase');
const BuildFixAgent = require('../ai/BuildFixAgent');

/**
 * 完整的Vue 2到Vue 3迁移流程，包含运行时错误修复
 */
class EnhancedMigrationOrchestrator extends MigrationOrchestrator {
  constructor(sourcePath, targetPath, options = {}) {
    super(sourcePath, targetPath, options);
    
    // 添加运行时错误修复阶段
    this.addRuntimeErrorRepairPhase();
  }

  /**
   * 添加运行时错误修复阶段
   */
  addRuntimeErrorRepairPhase() {
    const runtimePhase = new RuntimeErrorRepairPhase(this.targetPath, {
      timeout: this.options.runtimeMonitorTimeout || 60000, // 默认1分钟
      autoFix: this.options.autoFixRuntimeErrors !== false, // 默认启用
      verbose: this.options.verbose || false,
      port: this.options.devPort || 3000
    });

    // 在构建修复之后添加运行时修复阶段
    this.addPhase(runtimePhase, {
      after: '构建时错误修复',
      optional: true // 运行时修复是可选的
    });
  }

  /**
   * 执行增强的迁移流程
   */
  async executeMigration() {
    console.log(chalk.blue('🚀 开始增强的Vue 2到Vue 3迁移流程...'));
    
    try {
      // 执行标准迁移流程
      const migrationResult = await super.execute();
      
      // 如果迁移成功，输出运行时修复报告
      if (migrationResult.success && migrationResult.phases['运行时错误修复']) {
        this.outputRuntimeRepairReport(migrationResult.phases['运行时错误修复']);
      }
      
      return migrationResult;
      
    } catch (error) {
      console.error(chalk.red(`迁移失败: ${error.message}`));
      throw error;
    }
  }

  /**
   * 输出运行时修复报告
   */
  outputRuntimeRepairReport(runtimeResult) {
    if (!runtimeResult.report) return;
    
    const { summary } = runtimeResult.report;
    
    console.log(chalk.blue('\n📊 运行时错误修复总结:'));
    console.log(chalk.gray(`  监控时长: ${summary.duration}`));
    console.log(chalk.gray(`  检测错误: ${summary.totalErrors} 个`));
    
    if (summary.fixedErrors > 0) {
      console.log(chalk.green(`  ✅ 修复错误: ${summary.fixedErrors} 个`));
    }
    
    if (summary.pendingErrors > 0) {
      console.log(chalk.yellow(`  ⚠️  待处理错误: ${summary.pendingErrors} 个`));
    }
    
    console.log(chalk.gray(`  修复成功率: ${summary.fixRate}`));
    
    // 如果有未修复的错误，提供建议
    if (summary.pendingErrors > 0) {
      console.log(chalk.yellow('\n💡 建议:'));
      console.log(chalk.yellow('  - 手动检查剩余的运行时错误'));
      console.log(chalk.yellow('  - 考虑延长监控时间以捕获更多错误'));
      console.log(chalk.yellow('  - 检查错误日志获取详细信息'));
    }
  }
}

/**
 * 独立的运行时错误修复工具
 */
class RuntimeErrorRepairTool {
  constructor(projectPath, options = {}) {
    this.projectPath = projectPath;
    this.options = {
      timeout: 60000, // 1分钟
      autoFix: true,
      verbose: false,
      port: 3000,
      ...options
    };
  }

  /**
   * 执行运行时错误修复
   */
  async repair() {
    console.log(chalk.blue('🔧 开始运行时错误修复...'));
    
    try {
      const runtimePhase = new RuntimeErrorRepairPhase(this.projectPath, this.options);
      const result = await runtimePhase.execute();
      
      if (result.success) {
        console.log(chalk.green('✅ 运行时错误修复完成'));
        this.outputDetailedReport(result);
      } else {
        console.log(chalk.red('❌ 运行时错误修复失败'));
        console.error(chalk.red(result.error));
      }
      
      return result;
      
    } catch (error) {
      console.error(chalk.red(`运行时错误修复异常: ${error.message}`));
      throw error;
    }
  }

  /**
   * 输出详细报告
   */
  outputDetailedReport(result) {
    const { report, stats } = result;
    
    if (!report) return;
    
    console.log(chalk.blue('\n📋 详细报告:'));
    console.log(chalk.gray(`  开始时间: ${report.details.monitoringStarted}`));
    console.log(chalk.gray(`  结束时间: ${report.details.monitoringEnded}`));
    console.log(chalk.gray(`  自动修复: ${report.details.autoFixEnabled ? '启用' : '禁用'}`));
    
    if (stats) {
      console.log(chalk.blue('\n📊 统计信息:'));
      console.log(chalk.gray(`  总错误数: ${stats.totalErrors}`));
      console.log(chalk.gray(`  已修复: ${stats.fixedErrors}`));
      console.log(chalk.gray(`  待处理: ${stats.pendingErrors}`));
    }
  }
}

/**
 * 使用示例
 */
async function exampleUsage() {
  const projectPath = '/path/to/vue-project';
  
  // 示例1: 完整迁移流程（包含运行时修复）
  console.log(chalk.blue('示例1: 完整迁移流程'));
  const migrator = new EnhancedMigrationOrchestrator(
    '/path/to/vue2-project',
    '/path/to/vue3-project',
    {
      runtimeMonitorTimeout: 120000, // 2分钟监控
      autoFixRuntimeErrors: true,
      verbose: true
    }
  );
  
  try {
    const migrationResult = await migrator.executeMigration();
    console.log('迁移结果:', migrationResult.success ? '成功' : '失败');
  } catch (error) {
    console.error('迁移失败:', error.message);
  }
  
  // 示例2: 独立运行时错误修复
  console.log(chalk.blue('\n示例2: 独立运行时错误修复'));
  const repairTool = new RuntimeErrorRepairTool(projectPath, {
    timeout: 90000, // 1.5分钟
    autoFix: true,
    verbose: true,
    port: 3001
  });
  
  try {
    const repairResult = await repairTool.repair();
    console.log('修复结果:', repairResult.success ? '成功' : '失败');
  } catch (error) {
    console.error('修复失败:', error.message);
  }
  
  // 示例3: 手动配置运行时错误修复
  console.log(chalk.blue('\n示例3: 手动配置'));
  const customPhase = new RuntimeErrorRepairPhase(projectPath, {
    timeout: 30000, // 30秒快速检查
    autoFix: false, // 只监控，不自动修复
    verbose: true,
    maxErrors: 20
  });
  
  try {
    const customResult = await customPhase.execute();
    console.log('自定义修复结果:', customResult);
  } catch (error) {
    console.error('自定义修复失败:', error.message);
  }
}

/**
 * CLI集成示例
 */
function createCLIIntegration() {
  const { Command } = require('commander');
  const program = new Command();
  
  program
    .name('vue-runtime-repair')
    .description('Vue运行时错误修复工具')
    .version('1.0.0');
  
  program
    .command('repair <project-path>')
    .description('修复项目中的运行时错误')
    .option('-t, --timeout <ms>', '监控超时时间（毫秒）', '60000')
    .option('-p, --port <port>', '开发服务器端口', '3000')
    .option('--no-auto-fix', '禁用自动修复')
    .option('-v, --verbose', '详细输出')
    .action(async (projectPath, options) => {
      const repairTool = new RuntimeErrorRepairTool(projectPath, {
        timeout: parseInt(options.timeout),
        port: parseInt(options.port),
        autoFix: options.autoFix,
        verbose: options.verbose
      });
      
      try {
        await repairTool.repair();
      } catch (error) {
        console.error(chalk.red(`修复失败: ${error.message}`));
        process.exit(1);
      }
    });
  
  return program;
}

module.exports = {
  EnhancedMigrationOrchestrator,
  RuntimeErrorRepairTool,
  exampleUsage,
  createCLIIntegration
};

// 如果直接运行此文件，执行示例
if (require.main === module) {
  exampleUsage().catch(console.error);
}
