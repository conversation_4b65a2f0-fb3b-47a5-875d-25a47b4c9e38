# 运行时错误修复使用指南

## 快速开始

### 1. 基本使用

```javascript
const RuntimeErrorRepairPhase = require('./RuntimeErrorRepairPhase');

// 创建运行时错误修复阶段
const runtimePhase = new RuntimeErrorRepairPhase('/path/to/your/project', {
  timeout: 60000,  // 监控1分钟
  autoFix: true,   // 启用自动修复
  verbose: true    // 详细输出
});

// 执行修复
const result = await runtimePhase.execute();
console.log('修复结果:', result);
```

### 2. 集成到现有迁移流程

```javascript
const { EnhancedMigrationOrchestrator } = require('./integration-example');

const migrator = new EnhancedMigrationOrchestrator(
  '/path/to/vue2-project',
  '/path/to/vue3-project',
  {
    runtimeMonitorTimeout: 120000, // 2分钟监控
    autoFixRuntimeErrors: true,
    verbose: true
  }
);

await migrator.executeMigration();
```

### 3. 独立运行时错误修复工具

```javascript
const { RuntimeErrorRepairTool } = require('./integration-example');

const repairTool = new RuntimeErrorRepairTool('/path/to/project', {
  timeout: 90000,  // 1.5分钟
  autoFix: true,
  port: 3001
});

await repairTool.repair();
```

## 配置选项

### RuntimeErrorHandler 选项

```javascript
{
  port: 3000,                    // 服务器端口
  errorEndpoint: '/__runtime_errors__', // 错误接收端点
  maxErrors: 100,                // 最大错误数量
  autoFix: true,                 // 是否自动修复
  verbose: false                 // 详细日志
}
```

### RuntimeErrorRepairPhase 选项

```javascript
{
  timeout: 30000,                // 监控超时时间（毫秒）
  maxErrors: 50,                 // 最大错误数量
  autoFix: true,                 // 是否自动修复
  verbose: false,                // 详细日志
  devCommand: 'npm run dev',     // 开发服务器命令
  port: 3000                     // 开发服务器端口
}
```

### RuntimeErrorInjectionPlugin 选项

```javascript
{
  enabled: true,                 // 是否启用插件
  endpoint: '/__runtime_errors__', // 错误端点
  injectInProduction: false,     // 是否在生产环境注入
  verbose: false                 // 详细日志
}
```

## 使用场景

### 场景1: Vue 2到Vue 3迁移后的运行时验证

```javascript
// 在完成代码迁移后，验证运行时是否有错误
const runtimePhase = new RuntimeErrorRepairPhase(projectPath, {
  timeout: 120000, // 2分钟充分测试
  autoFix: true,
  verbose: true
});

const result = await runtimePhase.execute();

if (result.success && result.report.summary.totalErrors === 0) {
  console.log('✅ 迁移完成，无运行时错误');
} else {
  console.log(`⚠️ 发现 ${result.report.summary.totalErrors} 个运行时错误`);
}
```

### 场景2: 开发过程中的错误监控

```javascript
// 在开发过程中持续监控运行时错误
const runtimeHandler = new RuntimeErrorHandler(projectPath, {
  autoFix: false, // 开发时不自动修复，只收集错误
  verbose: true
});

// 在webpack配置中添加插件
const RuntimeErrorInjectionPlugin = require('./RuntimeErrorInjectionPlugin');

module.exports = {
  // ... 其他配置
  plugins: [
    new RuntimeErrorInjectionPlugin({
      enabled: process.env.NODE_ENV === 'development',
      verbose: true
    })
  ]
};
```

### 场景3: CI/CD中的自动化测试

```javascript
// 在CI/CD流程中自动检测运行时错误
const { RuntimeErrorRepairTool } = require('./integration-example');

async function ciRuntimeCheck() {
  const repairTool = new RuntimeErrorRepairTool(process.cwd(), {
    timeout: 180000, // 3分钟全面测试
    autoFix: false,  // CI中不自动修复
    verbose: true
  });

  const result = await repairTool.repair();
  
  if (result.stats.totalErrors > 0) {
    console.error(`❌ CI检测到 ${result.stats.totalErrors} 个运行时错误`);
    process.exit(1);
  }
  
  console.log('✅ CI运行时检查通过');
}
```

## 错误类型和修复策略

### 1. Vue响应式错误

**常见错误**:
```javascript
// 错误: 直接修改ref值
const count = ref(0);
count = 1; // ❌

// 修复: 使用.value
count.value = 1; // ✅
```

**AI修复策略**: 自动添加`.value`访问，转换为正确的响应式语法。

### 2. 组件生命周期错误

**常见错误**:
```javascript
// Vue 2 语法
export default {
  beforeDestroy() { // ❌ Vue 3中已废弃
    // 清理逻辑
  }
}
```

**AI修复策略**: 自动更新为Vue 3生命周期钩子名称。

### 3. 模板渲染错误

**常见错误**:
```vue
<!-- 错误: 访问可能为undefined的属性 -->
<div>{{ user.name }}</div> <!-- ❌ 如果user为undefined会报错 -->

<!-- 修复: 添加安全访问 -->
<div>{{ user?.name || '' }}</div> <!-- ✅ -->
```

**AI修复策略**: 自动添加可选链操作符和默认值。

### 4. 事件处理错误

**常见错误**:
```vue
<!-- 错误: 方法不存在 -->
<button @click="handleClick">Click</button> <!-- ❌ 如果handleClick未定义 -->
```

**AI修复策略**: 自动添加缺失的方法定义或修复方法名称。

## 最佳实践

### 1. 监控时间设置

- **快速检查**: 30-60秒，适用于CI/CD
- **开发测试**: 2-5分钟，适用于开发阶段
- **全面测试**: 10-15分钟，适用于发布前验证

### 2. 自动修复策略

- **开发环境**: 启用自动修复，提高开发效率
- **测试环境**: 禁用自动修复，收集错误信息
- **生产环境**: 不注入监控代码

### 3. 错误处理优先级

1. **致命错误**: 立即修复，阻止应用运行
2. **功能错误**: 优先修复，影响用户体验
3. **警告信息**: 延后修复，不影响核心功能

### 4. 集成建议

```javascript
// 推荐的集成方式
const migrationConfig = {
  // 基础迁移配置
  sourceFramework: 'vue2',
  targetFramework: 'vue3',
  
  // 运行时错误修复配置
  runtimeRepair: {
    enabled: true,
    timeout: 120000,
    autoFix: true,
    
    // 错误过滤器
    errorFilter: (error) => {
      // 忽略第三方库的错误
      return !error.fileName.includes('node_modules');
    },
    
    // 自定义修复策略
    customFixStrategies: {
      'vue-reactive': require('./custom-strategies/vue-reactive-fix'),
      'component-lifecycle': require('./custom-strategies/lifecycle-fix')
    }
  }
};
```

## 故障排除

### 常见问题

1. **开发服务器启动失败**
   - 检查端口是否被占用
   - 确认项目有有效的dev命令
   - 检查依赖是否正确安装

2. **错误监控代码未注入**
   - 确认webpack配置正确
   - 检查插件是否正确加载
   - 验证HTML模板存在

3. **AI修复失败**
   - 检查AI服务配置
   - 确认网络连接正常
   - 验证项目文件权限

### 调试技巧

```javascript
// 启用详细日志
const runtimePhase = new RuntimeErrorRepairPhase(projectPath, {
  verbose: true,
  debug: true // 额外的调试信息
});

// 手动检查错误收集
const stats = runtimeHandler.getErrorStats();
console.log('错误统计:', stats);

// 查看错误历史
const errors = Array.from(runtimeHandler.errorHistory.values());
console.log('错误详情:', errors);
```

## 扩展开发

### 自定义错误处理器

```javascript
class CustomRuntimeErrorHandler extends RuntimeErrorHandler {
  async handleRuntimeError(errorData) {
    // 自定义错误处理逻辑
    if (this.shouldIgnoreError(errorData)) {
      return;
    }
    
    // 调用父类方法
    return super.handleRuntimeError(errorData);
  }
  
  shouldIgnoreError(errorData) {
    // 自定义错误过滤逻辑
    return errorData.fileName.includes('third-party');
  }
}
```

### 自定义修复策略

```javascript
class CustomFixStrategy {
  async fixError(errorContext) {
    // 实现自定义修复逻辑
    const { fileName, message, stack } = errorContext;
    
    // 分析错误并生成修复方案
    const fixPlan = this.analyzeError(errorContext);
    
    // 应用修复
    return this.applyFix(fileName, fixPlan);
  }
}
```
