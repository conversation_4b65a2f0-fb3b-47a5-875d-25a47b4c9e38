const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const express = require('express');
const crypto = require('crypto');
const BuildFixAgent = require('../ai/BuildFixAgent');

/**
 * RuntimeErrorHandler - Vue运行时错误处理器
 * 
 * 功能：
 * 1. 在webpack dev server中添加错误接收路由
 * 2. 注入Vue错误处理器代码
 * 3. 收集运行时错误信息
 * 4. 通过AI分析并修复运行时错误
 * 5. 支持热重载和实时修复
 */
class RuntimeErrorHandler {
  constructor(projectPath, options = {}) {
    this.projectPath = projectPath;
    this.options = {
      port: 3000,
      errorEndpoint: '/__runtime_errors__',
      maxErrors: 100,
      autoFix: true,
      verbose: false,
      ...options
    };

    // 错误收集
    this.errorQueue = [];
    this.errorHistory = new Map(); // 错误去重
    this.fixAttempts = new Map(); // 修复尝试记录

    // AI修复代理
    this.buildFixAgent = new BuildFixAgent(projectPath, {
      maxAttempts: 3,
      verbose: this.options.verbose,
      ...options
    });

    // Express路由器
    this.router = express.Router();
    this.setupRoutes();
  }

  /**
   * 设置Express路由
   */
  setupRoutes() {
    // 接收运行时错误的POST路由
    this.router.post(this.options.errorEndpoint, express.json(), (req, res) => {
      this.handleRuntimeError(req.body);
      res.json({ success: true, message: 'Error received' });
    });

    // 获取错误历史的GET路由
    this.router.get(this.options.errorEndpoint, (req, res) => {
      res.json({
        errors: Array.from(this.errorHistory.values()),
        queueLength: this.errorQueue.length
      });
    });

    // 清除错误历史
    this.router.delete(this.options.errorEndpoint, (req, res) => {
      this.clearErrorHistory();
      res.json({ success: true, message: 'Error history cleared' });
    });
  }

  /**
   * 处理接收到的运行时错误
   */
  async handleRuntimeError(errorData) {
    try {
      const errorId = this.generateErrorId(errorData);
      
      // 检查是否为重复错误
      if (this.errorHistory.has(errorId)) {
        const existingError = this.errorHistory.get(errorId);
        existingError.count++;
        existingError.lastOccurred = new Date();
        return;
      }

      // 创建新的错误记录
      const errorRecord = {
        id: errorId,
        ...errorData,
        count: 1,
        firstOccurred: new Date(),
        lastOccurred: new Date(),
        fixed: false,
        fixAttempts: 0
      };

      this.errorHistory.set(errorId, errorRecord);
      this.errorQueue.push(errorRecord);

      if (this.options.verbose) {
        console.log(chalk.yellow(`🚨 运行时错误: ${errorData.message}`));
        console.log(chalk.gray(`   文件: ${errorData.fileName}:${errorData.lineNumber}`));
      }

      // 自动修复
      if (this.options.autoFix && !this.fixAttempts.has(errorId)) {
        await this.attemptAutoFix(errorRecord);
      }

    } catch (error) {
      console.error(chalk.red(`处理运行时错误失败: ${error.message}`));
    }
  }

  /**
   * 尝试自动修复错误
   */
  async attemptAutoFix(errorRecord) {
    const { id, fileName, message, stack, componentTrace } = errorRecord;
    
    try {
      this.fixAttempts.set(id, Date.now());
      errorRecord.fixAttempts++;

      if (this.options.verbose) {
        console.log(chalk.blue(`🔧 尝试修复运行时错误: ${message}`));
      }

      // 构建错误上下文信息
      const errorContext = this.buildErrorContext(errorRecord);
      
      // 使用BuildFixAgent进行修复
      const fixResult = await this.buildFixAgent.fixRuntimeError(errorContext);

      if (fixResult.success) {
        errorRecord.fixed = true;
        errorRecord.fixedAt = new Date();
        console.log(chalk.green(`✅ 运行时错误已修复: ${message}`));
      } else {
        console.log(chalk.yellow(`⚠️  运行时错误修复失败: ${fixResult.error}`));
      }

      return fixResult;

    } catch (error) {
      console.error(chalk.red(`自动修复异常: ${error.message}`));
      return { success: false, error: error.message };
    }
  }

  /**
   * 构建错误上下文信息
   */
  buildErrorContext(errorRecord) {
    const { fileName, lineNumber, columnNumber, message, stack, componentTrace } = errorRecord;
    
    return {
      type: 'runtime',
      fileName,
      lineNumber,
      columnNumber,
      message,
      stack,
      componentTrace,
      timestamp: errorRecord.firstOccurred,
      count: errorRecord.count,
      // 构建类似构建错误的输出格式
      buildOutput: this.formatAsBuilderError(errorRecord)
    };
  }

  /**
   * 将运行时错误格式化为类似构建错误的输出
   */
  formatAsBuilderError(errorRecord) {
    const { fileName, lineNumber, message, stack, componentTrace } = errorRecord;
    
    let output = `Runtime Error in ${fileName}:${lineNumber}\n`;
    output += `Error: ${message}\n\n`;
    
    if (stack) {
      output += `Stack Trace:\n${stack}\n\n`;
    }
    
    if (componentTrace && componentTrace.length > 0) {
      output += `Vue Component Trace:\n`;
      componentTrace.forEach((trace, index) => {
        output += `  ${index + 1}. ${trace.name || 'Anonymous'} (${trace.file}:${trace.line})\n`;
      });
      output += '\n';
    }
    
    return output;
  }

  /**
   * 生成错误ID用于去重
   */
  generateErrorId(errorData) {
    const { fileName, lineNumber, message } = errorData;
    const key = `${fileName}:${lineNumber}:${message}`;
    return crypto.createHash('md5').update(key).digest('hex').substring(0, 16);
  }

  /**
   * 清除错误历史
   */
  clearErrorHistory() {
    this.errorQueue = [];
    this.errorHistory.clear();
    this.fixAttempts.clear();
    console.log(chalk.green('✅ 错误历史已清除'));
  }

  /**
   * 获取Express路由器
   */
  getRouter() {
    return this.router;
  }

  /**
   * 获取错误统计信息
   */
  getErrorStats() {
    const totalErrors = this.errorHistory.size;
    const fixedErrors = Array.from(this.errorHistory.values()).filter(e => e.fixed).length;
    const pendingErrors = this.errorQueue.length;

    return {
      totalErrors,
      fixedErrors,
      pendingErrors,
      fixRate: totalErrors > 0 ? (fixedErrors / totalErrors * 100).toFixed(1) : 0
    };
  }

  /**
   * 生成错误处理器注入代码
   */
  generateInjectionCode() {
    const endpoint = this.options.errorEndpoint;
    
    return `
// Vue Runtime Error Handler - Auto-generated
(function() {
  const RUNTIME_ERROR_ENDPOINT = '${endpoint}';
  
  // 发送错误到服务器
  function sendErrorToServer(errorData) {
    fetch(RUNTIME_ERROR_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(errorData)
    }).catch(err => {
      console.warn('Failed to send runtime error to server:', err);
    });
  }
  
  // Vue错误处理器
  if (window.Vue && window.Vue.config) {
    // Vue 2
    window.Vue.config.errorHandler = function(err, vm, info) {
      const errorData = {
        message: err.message,
        fileName: err.fileName || 'unknown',
        lineNumber: err.lineNumber || 0,
        columnNumber: err.columnNumber || 0,
        stack: err.stack,
        vueInfo: info,
        componentTrace: vm ? getComponentTrace(vm) : [],
        timestamp: new Date().toISOString()
      };
      
      console.error('Vue Runtime Error:', err);
      sendErrorToServer(errorData);
    };
    
    window.Vue.config.warnHandler = function(msg, vm, trace) {
      const errorData = {
        message: msg,
        fileName: 'vue-warning',
        lineNumber: 0,
        columnNumber: 0,
        stack: trace,
        vueInfo: 'warning',
        componentTrace: vm ? getComponentTrace(vm) : [],
        timestamp: new Date().toISOString(),
        type: 'warning'
      };
      
      console.warn('Vue Warning:', msg);
      sendErrorToServer(errorData);
    };
  }
  
  // 获取组件追踪信息
  function getComponentTrace(vm) {
    const trace = [];
    let current = vm;
    
    while (current) {
      if (current.$options) {
        trace.push({
          name: current.$options.name || current.$options._componentTag,
          file: current.$options.__file,
          line: 0
        });
      }
      current = current.$parent;
    }
    
    return trace;
  }
  
  // 全局错误处理器
  window.addEventListener('error', function(event) {
    const errorData = {
      message: event.message,
      fileName: event.filename,
      lineNumber: event.lineno,
      columnNumber: event.colno,
      stack: event.error ? event.error.stack : '',
      timestamp: new Date().toISOString(),
      type: 'javascript'
    };
    
    sendErrorToServer(errorData);
  });
  
  // Promise rejection处理器
  window.addEventListener('unhandledrejection', function(event) {
    const errorData = {
      message: event.reason ? event.reason.message || event.reason : 'Unhandled Promise Rejection',
      fileName: 'promise',
      lineNumber: 0,
      columnNumber: 0,
      stack: event.reason ? event.reason.stack : '',
      timestamp: new Date().toISOString(),
      type: 'promise'
    };
    
    sendErrorToServer(errorData);
  });
})();
`;
  }
}

module.exports = RuntimeErrorHandler;
